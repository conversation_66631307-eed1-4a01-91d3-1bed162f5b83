@import url('https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&family=Roboto:ital,wght@0,100..900;1,100..900&family=Share+Tech&family=Tagesschrift&display=swap');

:root
{
	--green: green;
	--black: #130f40;
	--light-color: #666;
	--box-shadow:0 .5rem 1.5rem rgba(0,0,0,.1);

	--border: 2rem solid rgba(0,0,0.1);
	--outline: .1rem solid rgba(0,0,0.1);
}

*
{
	 font-family: "Roboto", sans-serif;
	 margin: 0;
	 padding: 0;
	 box-sizing: border-box;
	 outline: none;
	 border: none;
	 text-decoration: none;
	 text-transform: capitalize;
	 transition: all .2s linear;
}

html
{
	font-size: 62.5%;
	overflow-x: hidden;
	scroll-behavior: smooth;
	scroll-padding-top: 7rem;
}

body
{
	background: #eee;
}


/*css code for banner*/
section
{
	padding: 2rem 9%;
}

.heading
{
	text-align: center;
	padding: 2rem 0;
	padding-bottom: 3rem;
	font-size: 3rem;
	color: var(--black);
}

.heading span
{
	background: var(--green);
	color: #fff;
	display: inline-block;
	padding: .5rem 3rem;
	clip-path: polygon(100% 0, 93% 50%, 100% 99%, 0% 100%, 7% 50%, 0% 0%);
}

.btn 
{
	border: .2rem solid var(--black);
	margin-top: 1rem;
	display: inline-block;
	padding: .8rem 3rem;
	font-size: 1.7rem;
	border-radius: .5rem;
	color: var(--black);
	cursor: pointer;
	background: none;
}

.btn:hover
{
	background: var(--green);
	color: #fff;
}

.header
{
	border: 0px solid;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 2rem 9%;
	background: #fff;
	box-shadow: var(--box-shadow);
}

.header .logo
{
	font-size: 3rem;
	font-weight: bolder;
}

.header .logo i
{
	color: var(--green);
}

.header .navbar a 
{
	font-size: 1.7rem;
	margin: 0 1rem;
	color: var(--black);
}

.header .navbar a:hover,
.header .navbar a.active
{
	color: var(--green);
}

.header .navbar .login-link
{
	background: var(--green);
	color: white !important;
	padding: 0.8rem 1.5rem;
	border-radius: 0.5rem;
	transition: all 0.3s ease;
}

.header .navbar .login-link:hover
{
	background: #006400;
	transform: translateY(-2px);
}

.header .icons div
{
	border: 0px solid;
	height: 4.5rem;
	width: 4.5rem;
	line-height: 4.5rem;
	border-radius: .5rem;
	background: #eee;
	color: var(--black);
	font-size: 2rem;
	margin-right: .3rem;
	text-align: center;
	cursor: pointer;
	position: relative;
}

.header .icons div:hover
{
	background: var(--green);
	color: white;
}

/* Cart Badge */
.cart-badge {
    position: absolute;
    top: -0.8rem;
    right: -0.8rem;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 2.2rem;
    height: 2.2rem;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
    animation: bounce 0.3s ease;
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    80% {
        transform: translateY(-5px);
    }
}

/* User Profile Dropdown */
.user-profile {
    position: absolute;
    top: 110%;
    right: -110%;
    width: 30rem;
    background: white;
    border-radius: 1rem;
    box-shadow: var(--box-shadow);
    padding: 2rem;
    transition: all 0.4s ease;
}

.user-profile.active {
    right: 2rem;
}

.profile-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e1e5e9;
    margin-bottom: 2rem;
}

.avatar i {
    font-size: 4rem;
    color: var(--green);
}

.user-details h4 {
    font-size: 1.6rem;
    color: var(--black);
    margin-bottom: 0.5rem;
}

.user-details p {
    font-size: 1.2rem;
    color: var(--light-color);
}

.profile-menu a {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    color: var(--light-color);
    text-decoration: none;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
}

.profile-menu a:hover {
    background: #f8f9fa;
    color: var(--green);
}

.profile-menu a i {
    font-size: 1.6rem;
}

#menu-btn
{
	display: none;
}

.header .navbar.active
{
	right: 2rem;
	transition: .4s linear;
}

.header .search-form
{
	border: 0px solid;
	position: absolute;
	top: 120%;
	right: -110%;
	width: 50rem;
	height: 5rem;
	background: #fff;
	border-radius: .5rem;
	overflow: hidden;
	display: flex;
	align-items: center;
	box-shadow: var(--box-shadow);
}

.header .search-form.active
{
	right: 2rem;
	transition: .4s linear;
}


.header .search-form input
{
	height: 100%;
	width: 100%;
	background: none;
	text-transform: none;
	font-size: 1.7rem;
	color: var(--black);
	padding: 0 1.5rem;
}

.header .search-form label
{
	font-size: 2.2rem;
	padding-right: 1.5rem;
	color: var(--black);
	cursor: pointer;
}

.header .search-form label:hover
{
	color: var(--green);
}



.header .shopping-cart
{
	border: 1px solid;
	position: absolute;
	top: 110%;
	right: -110%;
	padding: 1rem;
	border-radius: .5rem;
	box-shadow: var(--box-shadow);
	width: 35rem;
	background: #fff;
}


.header .shopping-cart.active
{
	right: 2rem;
	transition: .4s linear;
}


.header .shopping-cart .box
{
	border: 0px solid blue;
	display: flex;
	align-items: center;
	gap: 1rem;
	position: relative;
	margin: 1rem 0;
}

.header .shopping-cart .box img
{
	height: 10rem;
}

.header .shopping-cart .box .fa-trash
{
	font-size: 2rem;
	position: absolute;
	top: 50%;
	right: 2rem;
	cursor: pointer;
	color: var(--light-color);
	transform: translate(-50%);
}

.header .shopping-cart .box .fa-trash:hover
{
	color: var(--green);
}

.header .shopping-cart .box .content h3
{
	color: var(--black);
	font-size: 1.8rem;
	padding-bottom: 1rem;
}

.header .shopping-cart .box .content span
{
	color: var(--light-color);
	font-size: 1.6rem;
}

.header .shopping-cart .box .content quantity
{
	padding-left: 1rem;
}

.header .shopping-cart .Total
{
	font-size: 2.5rem;
	padding: 1rem 0;
	text-align: center;
	color: var(--black);
}



.header .shopping-cart .btn 
{
	display: block;
	text-align: center;
	margin: 1rem 0;
}


 .header .login-form
{
	border: 0px solid;
	position: absolute;
	width: 35rem;
	top: 110%;
	right: -110%;
	box-shadow: var(--box-shadow);
	padding: 2rem;
	border-radius: .5rem;
	background: #fff;
	text-align: center;
}

.header .login-form.active
{
	right: 2rem;
	transition: .4s linear;
}

.header .login-form h3
{
	font-size: 2.6rem;
	text-transform: uppercase;
	color: var(--black);
}





.header .login-form .box 
{
	width: 100%;
	border: 0px solid;
	margin: .7rem 0;
	background: #eee;
	border-radius: .5rem;
	padding: 1rem;
	font-size: 1.6rem;
	color: var(--black);
	text-transform: none;
}






.header .login-form p 
{
	font-size: 1.5rem;
	padding: .5rem 0;
    color: var(--light-color);
}

.header .login-form p a 
{
	color: var(--green);
	text-decoration: underline;
}



.home
{
	border: 0px solid;
    display: flex;
	justify-content: center;
	background: url(../image/banner-img.jpg) no-repeat;
	background-position: center;
	background-size: cover;
	padding-top: 17rem;
	padding-bottom: 11rem;
}


.home .content
{
	border: 0px solid;
	text-align: center;
	width: 60rem;
}

.home .content h3
{
	color: var(--black);
	font-size: 3rem;
}

.home .content h3 span
{
	color: var(--green);
}

.home .content p 
{
	color: var(--light-color);
	font-size: 1.8rem;
    padding: 1rem 0;
    line-height: 1.8;
}


/*features*/
.features .box-container
{
	border: 0px solid;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
    gap: 2rem;
}

.features .box-container .box 
{
	border: 0px solid red;
	padding: 3rem 2rem;
	background: #fff;
    outline: var(--outline);
    outline-offset: -1rem;
    text-align: center;
    box-shadow: var(--box-shadow);
}

.features .box-container .box:hover
{
	box-shadow: 1px 1px 10px 4px var(--green);
}

 .features .box-container .box img
{
	margin: 1rem 0;
	height: 15rem;
}

.features .box-container .box h3
{
	font-size: 2rem;
	line-height: 1.8rem;
	color: var(--black);
}

.features .box-container .box p 
{
	font-size: 1rem;
	line-height: 1.8rem;
	color: var(--light-color);
	padding-bottom: 1rem 0;
}

/*features*/



/*products*/
.products .product-slider
{
	border: 0px solid;
	padding: 1rem;
}

.products .product-slider:first-child
{
	margin-bottom: 2rem;
}

.products .product-slider .box
{
	border: 0px solid red;
	background: #fff;
	border-radius: .5rem;
	text-align: center;
	padding: 3rem 2rem;
	outline-offset: -1rem;
	outline: var(--outline);
	box-shadow: var(--box-shadow);
	transition: .2s linear;

}

.products .product-slider .box:hover
{
	box-shadow: 1px 1px 10px 4px var(--green);
}

.products .product-slider .box img
{
	height: 20rem;
}

.products .product-slider .box h1
{
	font-size: 3rem;
	color: var(--black);
}

.products .product-slider .box .price 
{
	font-size: 2rem;
	color: var(--light-color);
	padding: .5rem 0;
}


.products .product-slider .box .stars i
{
	font-size: 1.7rem;
	color: orange;
	padding: .5rem 0;
}

.products .product-slider .box h3
{
	font-size: 2rem;
	color: var(--black);
}

/* Categories page styles */
.categories .box-container
{
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
	gap: 2rem;
}

.categories .box-container .box
{
	padding: 3rem 2rem;
	background: #fff;
	outline: var(--outline);
	outline-offset: -1rem;
	text-align: center;
	box-shadow: var(--box-shadow);
	border-radius: 1rem;
	transition: all 0.3s ease;
}

.categories .box-container .box:hover
{
	box-shadow: 1px 1px 10px 4px var(--green);
	transform: translateY(-5px);
}

.categories .box-container .box img
{
	margin: 1rem 0;
	height: 15rem;
}

.categories .box-container .box h3
{
	font-size: 2rem;
	color: var(--black);
	margin: 1rem 0;
}

.categories .box-container .box p
{
	font-size: 1.4rem;
	color: var(--light-color);
	line-height: 1.8;
	margin-bottom: 1rem;
}

/* Featured products section */
.featured-products
{
	background: #fff;
	padding: 2rem 9%;
}

.category-tabs
{
	display: flex;
	justify-content: center;
	gap: 1rem;
	margin-bottom: 3rem;
	flex-wrap: wrap;
}

.category-tabs .tab-btn
{
	background: none;
	border: 2px solid var(--green);
	padding: 1rem 2rem;
	font-size: 1.6rem;
	color: var(--green);
	cursor: pointer;
	border-radius: 0.5rem;
	transition: all 0.3s ease;
}

.category-tabs .tab-btn.active,
.category-tabs .tab-btn:hover
{
	background: var(--green);
	color: white;
}

.products-grid
{
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
	gap: 2rem;
}

.product-box
{
	background: #fff;
	border-radius: 1rem;
	text-align: center;
	padding: 2rem;
	outline-offset: -1rem;
	outline: var(--outline);
	box-shadow: var(--box-shadow);
	transition: all 0.3s ease;
}

.product-box:hover
{
	box-shadow: 1px 1px 10px 4px var(--green);
	transform: translateY(-5px);
}

.product-box img
{
	height: 15rem;
	margin-bottom: 1rem;
}

.product-box h3
{
	font-size: 1.8rem;
	color: var(--black);
	margin-bottom: 1rem;
}

.product-box .price
{
	font-size: 2rem;
	color: var(--light-color);
	margin-bottom: 1rem;
}

.product-box .stars
{
	margin-bottom: 1rem;
}

.product-box .stars i
{
	font-size: 1.5rem;
	color: orange;
}

/* Reviews page styles */
.reviews .box-container
{
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
	gap: 2rem;
}

.reviews .box-container .box
{
	padding: 3rem 2rem;
	background: #fff;
	outline: var(--outline);
	outline-offset: -1rem;
	text-align: center;
	box-shadow: var(--box-shadow);
	border-radius: 1rem;
	transition: all 0.3s ease;
}

.reviews .box-container .box:hover
{
	box-shadow: 1px 1px 10px 4px var(--green);
	transform: translateY(-5px);
}

.reviews .box-container .box img
{
	height: 10rem;
	width: 10rem;
	border-radius: 50%;
	object-fit: cover;
	margin-bottom: 1rem;
}

.reviews .box-container .box p
{
	font-size: 1.4rem;
	color: var(--light-color);
	line-height: 1.8;
	margin-bottom: 1rem;
	font-style: italic;
}

.reviews .box-container .box .stars
{
	margin-bottom: 1rem;
}

.reviews .box-container .box .stars i
{
	font-size: 1.5rem;
	color: orange;
}

.reviews .box-container .box h3
{
	font-size: 1.8rem;
	color: var(--black);
	margin-bottom: 0.5rem;
}

.reviews .box-container .box span
{
	font-size: 1.2rem;
	color: var(--green);
	text-transform: lowercase;
}

/* Review stats */
.review-stats
{
	background: var(--green);
	padding: 4rem 9%;
}

.review-stats .heading
{
	color: white;
}

.stats-container
{
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(20rem, 1fr));
	gap: 2rem;
}

.stat-box
{
	background: white;
	padding: 3rem 2rem;
	text-align: center;
	border-radius: 1rem;
	transition: transform 0.3s ease;
}

.stat-box:hover
{
	transform: translateY(-5px);
}

.stat-box i
{
	font-size: 4rem;
	color: var(--green);
	margin-bottom: 1rem;
}

.stat-box h3
{
	font-size: 3rem;
	color: var(--black);
	margin-bottom: 0.5rem;
}

.stat-box p
{
	font-size: 1.4rem;
	color: var(--light-color);
}

/* Write review section */
.write-review
{
	background: #f8f9fa;
	padding: 4rem 9%;
}

.review-form
{
	max-width: 60rem;
	margin: 0 auto;
	background: white;
	padding: 3rem;
	border-radius: 1rem;
	box-shadow: var(--box-shadow);
}

.input-group
{
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 1rem;
	margin-bottom: 2rem;
}

.input-group input
{
	padding: 1rem;
	border: 1px solid #ddd;
	border-radius: 0.5rem;
	font-size: 1.4rem;
}

.rating-input
{
	margin-bottom: 2rem;
}

.rating-input label
{
	display: block;
	margin-bottom: 1rem;
	font-size: 1.6rem;
	color: var(--black);
	font-weight: 600;
}

.stars-input
{
	display: flex;
	gap: 0.5rem;
}

.stars-input i
{
	font-size: 2rem;
	color: #ddd;
	cursor: pointer;
	transition: color 0.3s ease;
}

.stars-input i:hover,
.stars-input i.fas
{
	color: orange;
}

.review-form textarea
{
	width: 100%;
	padding: 1rem;
	border: 1px solid #ddd;
	border-radius: 0.5rem;
	font-size: 1.4rem;
	resize: vertical;
	margin-bottom: 2rem;
}

/* Blogs page styles */
.blogs .box-container
{
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(35rem, 1fr));
	gap: 2rem;
}

.blogs .box-container .box
{
	background: #fff;
	border-radius: 1rem;
	overflow: hidden;
	box-shadow: var(--box-shadow);
	transition: all 0.3s ease;
}

.blogs .box-container .box:hover
{
	box-shadow: 1px 1px 10px 4px var(--green);
	transform: translateY(-5px);
}

.blogs .box-container .box img
{
	width: 100%;
	height: 20rem;
	object-fit: cover;
}

.blogs .box-container .box .content
{
	padding: 2rem;
}

.blogs .box-container .box .content .icons
{
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 1rem;
}

.blogs .box-container .box .content .icons a
{
	font-size: 1.2rem;
	color: var(--light-color);
	text-decoration: none;
}

.blogs .box-container .box .content .icons a:hover
{
	color: var(--green);
}

.blogs .box-container .box .content .icons i
{
	margin-right: 0.5rem;
}

.blogs .box-container .box .content h3
{
	font-size: 2rem;
	color: var(--black);
	margin-bottom: 1rem;
}

.blogs .box-container .box .content p
{
	font-size: 1.4rem;
	color: var(--light-color);
	line-height: 1.8;
	margin-bottom: 1rem;
}

/* Blog categories */
.blog-categories
{
	background: #f8f9fa;
	padding: 4rem 9%;
}

.category-container
{
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
	gap: 2rem;
}

.category-box
{
	background: white;
	padding: 3rem 2rem;
	text-align: center;
	border-radius: 1rem;
	box-shadow: var(--box-shadow);
	transition: all 0.3s ease;
}

.category-box:hover
{
	box-shadow: 1px 1px 10px 4px var(--green);
	transform: translateY(-5px);
}

.category-box i
{
	font-size: 4rem;
	color: var(--green);
	margin-bottom: 1rem;
}

.category-box h3
{
	font-size: 2rem;
	color: var(--black);
	margin-bottom: 1rem;
}

.category-box p
{
	font-size: 1.4rem;
	color: var(--light-color);
	margin-bottom: 1rem;
}

.category-box .post-count
{
	font-size: 1.2rem;
	color: var(--green);
	font-weight: 600;
}

/* Newsletter section */
.newsletter
{
	background: var(--green);
	padding: 4rem 9%;
	text-align: center;
}

.newsletter .heading
{
	color: white;
}

.newsletter-content p
{
	color: white;
	font-size: 1.6rem;
	margin-bottom: 2rem;
	max-width: 60rem;
	margin-left: auto;
	margin-right: auto;
}

.newsletter-form
{
	display: flex;
	max-width: 50rem;
	margin: 0 auto 2rem;
	gap: 1rem;
}

.newsletter-form input[type="email"]
{
	flex: 1;
	padding: 1rem;
	border: none;
	border-radius: 0.5rem;
	font-size: 1.4rem;
}

.newsletter-form .btn
{
	background: var(--black);
	color: white;
	border: none;
}

.social-links
{
	display: flex;
	justify-content: center;
	gap: 1rem;
}

.social-links a
{
	display: inline-block;
	width: 4rem;
	height: 4rem;
	background: white;
	color: var(--green);
	text-align: center;
	line-height: 4rem;
	border-radius: 50%;
	font-size: 1.8rem;
	transition: all 0.3s ease;
}

.social-links a:hover
{
	background: var(--black);
	color: white;
	transform: translateY(-3px);
}

/* Responsive design */
@media (max-width: 991px) {
	html {
		font-size: 55%;
	}

	.header {
		padding: 2rem;
	}

	section {
		padding: 2rem;
	}
}

@media (max-width: 768px) {
	#menu-btn {
		display: inline-block;
	}

	.header .navbar {
		position: absolute;
		top: 110%;
		right: -110%;
		width: 30rem;
		box-shadow: var(--box-shadow);
		border-radius: .5rem;
		background: #fff;
	}

	.header .navbar.active {
		right: 2rem;
	}

	.header .navbar a {
		font-size: 2rem;
		margin: 2rem 2.5rem;
		display: block;
	}

	.input-group {
		grid-template-columns: 1fr;
	}

	.newsletter-form {
		flex-direction: column;
	}
}

@media (max-width: 450px) {
	html {
		font-size: 50%;
	}
}

}

/*products*/








/* media queires */
@media (max-width: 991px)
{


	html
    {
	font-size: 55%;
    }

    .header
    {
    	padding: 2rem;
    }
    section
    {
    	padding: 2rem;
    }
}

@media (max-width: 768px)
{
	#menu-btn
    {
	display: inline-block;
    }

	.header .navbar
	{
		position: absolute;
		top: 110%;
		right: -110%;
		width: 30rem;
		box-shadow: var(--box-shadow);
		border-radius: .5rem;
		background: #fff;
	}

	.header .search-form
	{
		width: 90%;
	}

    .header .navbar a
	{
		font-size: 2rem;
		margin: 2rem 2.5rem;
		display: block;
	}
}


@media (max-width: 450px)
{
    html
    {
	font-size: 50%;
    }
    .heading
    {
    	font-size: 2.5rem;
    }
}

