<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Blogs - Glossary Store</title>
	
	<!-- Font Awesome CDN -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	
	<!-- Custom CSS -->
	<link rel="stylesheet" type="text/css" href="css/style.css">
</head>
<body>

<!-- header section  -->
<header class="header"> 
    <a href="index.html" class="logo"><i class="fas fa-shopping-basket"></i> Glossary Store</a>

<nav class="navbar">
	<a href="index.html">home</a>
	<a href="index.html#features">features</a>
	<a href="index.html#products">products</a>
	<a href="categories.html">categories</a>
	<a href="reviews.html">reviews</a>
	<a href="blogs.html" class="active">blogs</a>
	<a href="admin.html" class="admin-link">admin</a>
</nav>

<div class="icons">
	<div class="fas fa-bars" id="menu-btn"></div>
	<div class="fas fa-search" id="search-btn"></div>
	<div class="fas fa-shopping-cart" id="cart-btn"></div>
	<div class="fas fa-user" id="login-btn"></div>
</div>

<form class="search-form">
	<input type="search" id="search-box" placeholder="Search blogs...">
	<label for="search-box" class="fas fa-search"></label>
</form>

<div class="shopping-cart">
	<div class="box">
		<i class="fas fa-trash"></i>
		<img src="image/cart-img-2.png">
		<div class="content">
			<h3>onion</h3>
			<span class="price">$4/-</span>
			<span class="quantity">qty : 09</span>
		</div>
	</div>
	<div class="Total"> Total : $23.3/- </div>
	<a href="#" class="btn">Checkout</a>
</div>

<form action="#" class="login-form">
	<h3>login now</h3>
	<input type="email" placeholder="your email" class="box">
    <input type="password" placeholder="your password" class="box">
<p>Forget Your Password <a href="#"> Click Here</a></p>
<p>Don't Have An Account <a href="#"> Create Now</a></p>
    <input type="submit" value="login now" class="btn">
</form>

</header>
<!-- header section  -->

<!-- blogs section  -->
<section class="blogs" id="blogs" style="padding-top: 12rem;">
	<h1 class="heading"> our <span>blogs</span></h1>

    <div class="box-container">
    	<div class="box">
    		<img src="image/blog-1.jpg" alt="Healthy Eating">
    		<div class="content">
    			<div class="icons">
    				<a href="#"><i class="fas fa-user"></i> by admin</a>
    				<a href="#"><i class="fas fa-calendar"></i> 1st may, 2024</a>
    			</div>
    			<h3>Benefits of Organic Food</h3>
    			<p>Discover the amazing health benefits of choosing organic foods for you and your family. Learn why organic produce is worth the investment...</p>
    			<a href="#" class="btn">read more</a>
    		</div>
    	</div>

    	<div class="box">
    		<img src="image/blog-2.jpg" alt="Fresh Vegetables">
    		<div class="content">
    			<div class="icons">
    				<a href="#"><i class="fas fa-user"></i> by nutritionist</a>
    				<a href="#"><i class="fas fa-calendar"></i> 15th april, 2024</a>
    			</div>
    			<h3>Seasonal Vegetables Guide</h3>
    			<p>A comprehensive guide to seasonal vegetables and how to incorporate them into your daily meals for maximum nutrition and flavor...</p>
    			<a href="#" class="btn">read more</a>
    		</div>
    	</div>

    	<div class="box">
    		<img src="image/blog-3.jpg" alt="Grocery Shopping">
    		<div class="content">
    			<div class="icons">
    				<a href="#"><i class="fas fa-user"></i> by chef</a>
    				<a href="#"><i class="fas fa-calendar"></i> 28th march, 2024</a>
    			</div>
    			<h3>Smart Grocery Shopping Tips</h3>
    			<p>Learn how to shop smarter, save money, and reduce food waste with these expert grocery shopping tips and tricks...</p>
    			<a href="#" class="btn">read more</a>
    		</div>
    	</div>

    	<div class="box">
    		<img src="image/product-10.jpg" alt="Meal Planning">
    		<div class="content">
    			<div class="icons">
    				<a href="#"><i class="fas fa-user"></i> by dietitian</a>
    				<a href="#"><i class="fas fa-calendar"></i> 10th march, 2024</a>
    			</div>
    			<h3>Weekly Meal Planning Made Easy</h3>
    			<p>Simplify your life with effective meal planning strategies. Save time, money, and eat healthier with our step-by-step guide...</p>
    			<a href="#" class="btn">read more</a>
    		</div>
    	</div>

    	<div class="box">
    		<img src="image/product-11.jpg" alt="Food Storage">
    		<div class="content">
    			<div class="icons">
    				<a href="#"><i class="fas fa-user"></i> by expert</a>
    				<a href="#"><i class="fas fa-calendar"></i> 25th february, 2024</a>
    			</div>
    			<h3>Proper Food Storage Techniques</h3>
    			<p>Extend the life of your groceries with proper storage techniques. Learn the best ways to store fruits, vegetables, and other perishables...</p>
    			<a href="#" class="btn">read more</a>
    		</div>
    	</div>

    	<div class="box">
    		<img src="image/product-12.jpeg" alt="Healthy Recipes">
    		<div class="content">
    			<div class="icons">
    				<a href="#"><i class="fas fa-user"></i> by chef maria</a>
    				<a href="#"><i class="fas fa-calendar"></i> 12th february, 2024</a>
    			</div>
    			<h3>Quick & Healthy Recipe Ideas</h3>
    			<p>Delicious and nutritious recipes that can be prepared in 30 minutes or less. Perfect for busy weekdays and healthy living...</p>
    			<a href="#" class="btn">read more</a>
    		</div>
    	</div>
    </div>

</section>
<!-- blogs section  -->

<!-- blog categories -->
<section class="blog-categories">
	<h1 class="heading"> blog <span>categories</span></h1>
	
	<div class="category-container">
		<div class="category-box">
			<i class="fas fa-leaf"></i>
			<h3>Healthy Living</h3>
			<p>Tips and advice for a healthier lifestyle</p>
			<span class="post-count">12 posts</span>
		</div>
		
		<div class="category-box">
			<i class="fas fa-utensils"></i>
			<h3>Recipes</h3>
			<p>Delicious and easy-to-make recipes</p>
			<span class="post-count">8 posts</span>
		</div>
		
		<div class="category-box">
			<i class="fas fa-shopping-cart"></i>
			<h3>Shopping Tips</h3>
			<p>Smart shopping and money-saving tips</p>
			<span class="post-count">6 posts</span>
		</div>
		
		<div class="category-box">
			<i class="fas fa-seedling"></i>
			<h3>Organic Living</h3>
			<p>Benefits of organic and sustainable living</p>
			<span class="post-count">10 posts</span>
		</div>
	</div>
</section>

<!-- newsletter subscription -->
<section class="newsletter">
	<h1 class="heading"> subscribe to our <span>newsletter</span></h1>
	
	<div class="newsletter-content">
		<p>Stay updated with our latest blog posts, recipes, and healthy living tips. Get exclusive offers and discounts delivered to your inbox!</p>
		
		<form class="newsletter-form">
			<input type="email" placeholder="Enter your email address" required>
			<input type="submit" value="Subscribe" class="btn">
		</form>
		
		<div class="social-links">
			<a href="#"><i class="fab fa-facebook-f"></i></a>
			<a href="#"><i class="fab fa-twitter"></i></a>
			<a href="#"><i class="fab fa-instagram"></i></a>
			<a href="#"><i class="fab fa-youtube"></i></a>
		</div>
	</div>
</section>

<script src="js/script.js"></script>
<script>
// Newsletter form submission
document.querySelector('.newsletter-form').addEventListener('submit', function(e) {
    e.preventDefault();
    const email = this.querySelector('input[type="email"]').value;
    alert('Thank you for subscribing! We will send updates to: ' + email);
    this.reset();
});

// Blog search functionality
document.getElementById('search-box').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const blogBoxes = document.querySelectorAll('.blogs .box');
    
    blogBoxes.forEach(box => {
        const title = box.querySelector('h3').textContent.toLowerCase();
        const content = box.querySelector('p').textContent.toLowerCase();
        
        if (title.includes(searchTerm) || content.includes(searchTerm)) {
            box.style.display = 'block';
        } else {
            box.style.display = 'none';
        }
    });
});
</script>

</body>
</html>
