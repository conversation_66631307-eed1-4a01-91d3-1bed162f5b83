<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Checkout - Glossary Store</title>
	
	<!-- Font Awesome CDN -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	
	<!-- Custom CSS -->
	<link rel="stylesheet" type="text/css" href="css/style.css">
	<link rel="stylesheet" type="text/css" href="css/checkout.css">
</head>
<body>

<!-- header section  -->
<header class="header"> 
    <a href="index.html" class="logo"><i class="fas fa-shopping-basket"></i> Glossary Store</a>

<nav class="navbar">
	<a href="index.html">home</a>
	<a href="index.html#features">features</a>
	<a href="index.html#products">products</a>
	<a href="categories.html">categories</a>
	<a href="reviews.html">reviews</a>
	<a href="blogs.html">blogs</a>
	<a href="login.html" class="login-link">login</a>
	<a href="admin.html" class="admin-link">admin</a>
</nav>

<div class="icons">
	<div class="fas fa-bars" id="menu-btn"></div>
	<div class="fas fa-search" id="search-btn"></div>
	<div class="fas fa-shopping-cart" id="cart-btn">
		<span class="cart-badge" id="cart-count">0</span>
	</div>
	<div class="fas fa-user" id="login-btn"></div>
</div>

<form class="search-form">
	<input type="search" id="search-box" placeholder="Search products...">
	<label for="search-box" class="fas fa-search"></label>
</form>

<div class="shopping-cart">
	<div id="cart-items">
		<!-- Cart items will be loaded here -->
	</div>
	<div class="Total" id="cart-total">Total: $0.00</div>
	<a href="checkout.html" class="btn" id="checkout-btn">Checkout</a>
</div>

<!-- User Profile Dropdown -->
<div class="user-profile" id="user-profile">
	<div class="profile-info" id="profile-info">
		<div class="avatar">
			<i class="fas fa-user-circle"></i>
		</div>
		<div class="user-details">
			<h4 id="user-name">Guest User</h4>
			<p id="user-email">Please login</p>
		</div>
	</div>
	<div class="profile-menu">
		<a href="profile.html"><i class="fas fa-user"></i> My Profile</a>
		<a href="orders.html"><i class="fas fa-shopping-bag"></i> My Orders</a>
		<a href="wishlist.html"><i class="fas fa-heart"></i> Wishlist</a>
		<a href="addresses.html"><i class="fas fa-map-marker-alt"></i> Addresses</a>
		<a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
	</div>
</div>

</header>
<!-- header section  -->

<!-- checkout section -->
<section class="checkout-section">
	<div class="checkout-container">
		<!-- Progress Steps -->
		<div class="checkout-progress">
			<div class="step active">
				<div class="step-number">1</div>
				<div class="step-title">Cart Review</div>
			</div>
			<div class="step">
				<div class="step-number">2</div>
				<div class="step-title">Shipping</div>
			</div>
			<div class="step">
				<div class="step-number">3</div>
				<div class="step-title">Payment</div>
			</div>
			<div class="step">
				<div class="step-number">4</div>
				<div class="step-title">Confirmation</div>
			</div>
		</div>

		<div class="checkout-content">
			<!-- Step 1: Cart Review -->
			<div class="checkout-step active" id="step-1">
				<h2>Review Your Order</h2>
				<div class="order-items" id="order-items">
					<!-- Order items will be loaded here -->
				</div>
				<div class="order-summary">
					<div class="summary-row">
						<span>Subtotal:</span>
						<span id="subtotal">$0.00</span>
					</div>
					<div class="summary-row">
						<span>Shipping:</span>
						<span id="shipping">$5.99</span>
					</div>
					<div class="summary-row">
						<span>Tax:</span>
						<span id="tax">$0.00</span>
					</div>
					<div class="summary-row total">
						<span>Total:</span>
						<span id="final-total">$0.00</span>
					</div>
				</div>
				<button class="checkout-btn" onclick="nextStep()">Continue to Shipping</button>
			</div>

			<!-- Step 2: Shipping Information -->
			<div class="checkout-step" id="step-2">
				<h2>Shipping Information</h2>
				<form class="shipping-form">
					<div class="form-row">
						<div class="form-group">
							<label>First Name</label>
							<input type="text" id="shipping-first-name" required>
						</div>
						<div class="form-group">
							<label>Last Name</label>
							<input type="text" id="shipping-last-name" required>
						</div>
					</div>
					<div class="form-group">
						<label>Email Address</label>
						<input type="email" id="shipping-email" required>
					</div>
					<div class="form-group">
						<label>Phone Number</label>
						<input type="tel" id="shipping-phone" required>
					</div>
					<div class="form-group">
						<label>Street Address</label>
						<input type="text" id="shipping-address" required>
					</div>
					<div class="form-row">
						<div class="form-group">
							<label>City</label>
							<input type="text" id="shipping-city" required>
						</div>
						<div class="form-group">
							<label>State</label>
							<input type="text" id="shipping-state" required>
						</div>
						<div class="form-group">
							<label>ZIP Code</label>
							<input type="text" id="shipping-zip" required>
						</div>
					</div>
					<div class="delivery-options">
						<h3>Delivery Options</h3>
						<label class="radio-option">
							<input type="radio" name="delivery" value="standard" checked>
							<span class="radio-custom"></span>
							<div class="option-details">
								<strong>Standard Delivery (5-7 days)</strong>
								<span>$5.99</span>
							</div>
						</label>
						<label class="radio-option">
							<input type="radio" name="delivery" value="express">
							<span class="radio-custom"></span>
							<div class="option-details">
								<strong>Express Delivery (2-3 days)</strong>
								<span>$12.99</span>
							</div>
						</label>
						<label class="radio-option">
							<input type="radio" name="delivery" value="overnight">
							<span class="radio-custom"></span>
							<div class="option-details">
								<strong>Overnight Delivery</strong>
								<span>$24.99</span>
							</div>
						</label>
					</div>
				</form>
				<div class="step-buttons">
					<button class="checkout-btn secondary" onclick="prevStep()">Back to Cart</button>
					<button class="checkout-btn" onclick="nextStep()">Continue to Payment</button>
				</div>
			</div>

			<!-- Step 3: Payment Information -->
			<div class="checkout-step" id="step-3">
				<h2>Payment Information</h2>
				<div class="payment-methods">
					<label class="payment-option">
						<input type="radio" name="payment" value="card" checked>
						<span class="radio-custom"></span>
						<div class="option-details">
							<i class="fas fa-credit-card"></i>
							<span>Credit/Debit Card</span>
						</div>
					</label>
					<label class="payment-option">
						<input type="radio" name="payment" value="paypal">
						<span class="radio-custom"></span>
						<div class="option-details">
							<i class="fab fa-paypal"></i>
							<span>PayPal</span>
						</div>
					</label>
					<label class="payment-option">
						<input type="radio" name="payment" value="cod">
						<span class="radio-custom"></span>
						<div class="option-details">
							<i class="fas fa-money-bill-wave"></i>
							<span>Cash on Delivery</span>
						</div>
					</label>
				</div>

				<div class="card-form" id="card-form">
					<div class="form-group">
						<label>Card Number</label>
						<input type="text" id="card-number" placeholder="1234 5678 9012 3456" maxlength="19">
						<div class="card-icons">
							<i class="fab fa-cc-visa"></i>
							<i class="fab fa-cc-mastercard"></i>
							<i class="fab fa-cc-amex"></i>
						</div>
					</div>
					<div class="form-row">
						<div class="form-group">
							<label>Expiry Date</label>
							<input type="text" id="card-expiry" placeholder="MM/YY" maxlength="5">
						</div>
						<div class="form-group">
							<label>CVV</label>
							<input type="text" id="card-cvv" placeholder="123" maxlength="4">
						</div>
					</div>
					<div class="form-group">
						<label>Cardholder Name</label>
						<input type="text" id="card-name" placeholder="John Doe">
					</div>
				</div>

				<div class="step-buttons">
					<button class="checkout-btn secondary" onclick="prevStep()">Back to Shipping</button>
					<button class="checkout-btn" onclick="nextStep()">Review Order</button>
				</div>
			</div>

			<!-- Step 4: Order Confirmation -->
			<div class="checkout-step" id="step-4">
				<h2>Order Confirmation</h2>
				<div class="confirmation-content">
					<div class="success-icon">
						<i class="fas fa-check-circle"></i>
					</div>
					<h3>Order Placed Successfully!</h3>
					<p>Thank you for your order. We'll send you a confirmation email shortly.</p>
					
					<div class="order-details">
						<div class="detail-row">
							<span>Order Number:</span>
							<span id="order-number">#ORD-001</span>
						</div>
						<div class="detail-row">
							<span>Estimated Delivery:</span>
							<span id="delivery-date">5-7 business days</span>
						</div>
						<div class="detail-row">
							<span>Total Amount:</span>
							<span id="order-total">$0.00</span>
						</div>
					</div>

					<div class="confirmation-actions">
						<button class="checkout-btn" onclick="window.location.href='orders.html'">View Order Details</button>
						<button class="checkout-btn secondary" onclick="window.location.href='index.html'">Continue Shopping</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<script src="js/script.js"></script>
<script src="js/auth.js"></script>
<script src="js/checkout.js"></script>

</body>
</html>
