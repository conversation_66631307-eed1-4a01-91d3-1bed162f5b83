<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Categories - Glossary Store</title>
	
	<!-- Font Awesome CDN -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	
	<!-- Custom CSS -->
	<link rel="stylesheet" type="text/css" href="css/style.css">
</head>
<body>

<!-- header section  -->
<header class="header"> 
    <a href="index.html" class="logo"><i class="fas fa-shopping-basket"></i> Glossary Store</a>

<nav class="navbar">
	<a href="index.html">home</a>
	<a href="index.html#features">features</a>
	<a href="index.html#products">products</a>
	<a href="categories.html" class="active">categories</a>
	<a href="reviews.html">reviews</a>
	<a href="blogs.html">blogs</a>
	<a href="login.html" class="login-link" id="navbar-login">login</a>
	<a href="admin.html" class="admin-link" id="navbar-admin" style="display: none;">admin panel</a>
</nav>

<div class="icons">
	<div class="fas fa-bars" id="menu-btn"></div>
	<div class="fas fa-search" id="search-btn"></div>
	<div class="fas fa-shopping-cart" id="cart-btn">
		<span class="cart-badge" id="cart-count">0</span>
	</div>
	<div class="fas fa-user" id="login-btn"></div>
</div>

<form class="search-form">
	<input type="search" id="search-box" placeholder="Search categories...">
	<label for="search-box" class="fas fa-search"></label>
</form>

<div class="shopping-cart">
	<div id="cart-items">
		<p class="empty-cart">Your cart is empty</p>
	</div>
	<div class="Total" id="cart-total">Total: $0.00</div>
	<a href="checkout.html" class="btn" id="checkout-btn" style="display: none;">Checkout</a>
</div>

<!-- User Profile Dropdown -->
<div class="user-profile" id="user-profile">
	<div class="profile-info" id="profile-info">
		<div class="avatar">
			<i class="fas fa-user-circle"></i>
		</div>
		<div class="user-details">
			<h4 id="user-name">Guest User</h4>
			<p id="user-email">Please login</p>
		</div>
	</div>
	<div class="profile-menu">
		<a href="profile.html"><i class="fas fa-user"></i> My Profile</a>
		<a href="orders.html"><i class="fas fa-shopping-bag"></i> My Orders</a>
		<a href="wishlist.html"><i class="fas fa-heart"></i> Wishlist</a>
		<a href="addresses.html"><i class="fas fa-map-marker-alt"></i> Addresses</a>
		<a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
	</div>
</div>

</header>
<!-- header section  -->

<!-- categories section  -->
<section class="categories" id="categories" style="padding-top: 12rem;">
	<h1 class="heading"> product <span>categories</span></h1>

    <div class="box-container">
    	<div class="box">
    		<img src="image/cat-1.png" alt="Fruits">
    		<h3>Fresh Fruits</h3>
    		<p>Organic and fresh fruits delivered to your doorstep</p>
    		<a href="#" class="btn">shop now</a>
    	</div>

    	<div class="box">
    		<img src="image/cat-2.png" alt="Vegetables">
    		<h3>Fresh Vegetables</h3>
    		<p>Farm fresh vegetables for healthy living</p>
    		<a href="#" class="btn">shop now</a>
    	</div>

    	<div class="box">
    		<img src="image/cat-3.png" alt="Dairy">
    		<h3>Dairy Products</h3>
    		<p>Pure and fresh dairy products from trusted sources</p>
    		<a href="#" class="btn">shop now</a>
    	</div>

    	<div class="box">
    		<img src="image/cat-4.png" alt="Meat">
    		<h3>Fresh Meat</h3>
    		<p>Premium quality meat and poultry products</p>
    		<a href="#" class="btn">shop now</a>
    	</div>

    	<div class="box">
    		<img src="image/product-7.png" alt="Grains">
    		<h3>Grains & Cereals</h3>
    		<p>Nutritious grains and cereals for healthy meals</p>
    		<a href="#" class="btn">shop now</a>
    	</div>

    	<div class="box">
    		<img src="image/product-8.png" alt="Beverages">
    		<h3>Beverages</h3>
    		<p>Refreshing drinks and healthy beverages</p>
    		<a href="#" class="btn">shop now</a>
    	</div>
    </div>

</section>
<!-- categories section  -->

<!-- featured products by category -->
<section class="featured-products">
	<h1 class="heading"> featured <span>products</span></h1>
	
	<div class="category-tabs">
		<button class="tab-btn active" data-category="fruits">Fruits</button>
		<button class="tab-btn" data-category="vegetables">Vegetables</button>
		<button class="tab-btn" data-category="dairy">Dairy</button>
		<button class="tab-btn" data-category="meat">Meat</button>
	</div>

	<div class="products-grid" id="products-grid">
		<!-- Products will be loaded here dynamically -->
		<div class="product-box" data-product-id="1">
			<img src="image/product-1.png" alt="Orange">
			<h3>Fresh Orange</h3>
			<div class="price">$12.99</div>
			<div class="stars">
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star-half-alt"></i>
			</div>
			<a href="#" class="btn" data-product-id="1">add to cart</a>
		</div>

		<div class="product-box" data-product-id="6">
			<img src="image/product-6.png" alt="Avocado">
			<h3>Fresh Avocado</h3>
			<div class="price">$15.99</div>
			<div class="stars">
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
			</div>
			<a href="#" class="btn" data-product-id="6">add to cart</a>
		</div>

		<div class="product-box" data-product-id="7">
			<img src="image/product-1.png" alt="Apple">
			<h3>Red Apple</h3>
			<div class="price">$8.99</div>
			<div class="stars">
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="far fa-star"></i>
			</div>
			<a href="#" class="btn" data-product-id="7">add to cart</a>
		</div>
	</div>
</section>

<script src="js/script.js"></script>
<script src="js/auth.js"></script>
<script>
// Category tab functionality
document.querySelectorAll('.tab-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        // Remove active class from all buttons
        document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
        // Add active class to clicked button
        this.classList.add('active');

        // Here you would typically load products for the selected category
        const category = this.dataset.category;
        console.log('Loading products for category:', category);
    });
});
</script>

</body>
</html>
