<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Admin Panel - Glossary Store</title>
	
	<!-- Font Awesome CDN -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	
	<!-- Custom CSS -->
	<link rel="stylesheet" type="text/css" href="css/style.css">
	<link rel="stylesheet" type="text/css" href="css/admin.css">
	<link rel="stylesheet" type="text/css" href="css/management.css">
</head>
<body>

<!-- header section  -->
<header class="header"> 
    <a href="index.html" class="logo"><i class="fas fa-shopping-basket"></i> Glossary Store</a>

<nav class="navbar">
	<a href="index.html">home</a>
	<a href="index.html#features">features</a>
	<a href="index.html#products">products</a>
	<a href="categories.html">categories</a>
	<a href="reviews.html">reviews</a>
	<a href="blogs.html">blogs</a>
	<a href="login.html" class="login-link" id="navbar-login" style="display: none;">login</a>
	<a href="admin.html" class="active admin-link" id="navbar-admin">admin panel</a>
</nav>

<div class="icons">
	<div class="fas fa-bars" id="menu-btn"></div>
	<div class="fas fa-search" id="search-btn"></div>
	<div class="fas fa-shopping-cart" id="cart-btn"></div>
	<div class="fas fa-user" id="login-btn"></div>
</div>

<form class="search-form">
	<input type="search" id="search-box" placeholder="Search products...">
	<label for="search-box" class="fas fa-search"></label>
</form>

<div class="shopping-cart">
	<div class="box">
		<i class="fas fa-trash"></i>
		<img src="image/cart-img-2.png">
		<div class="content">
			<h3>onion</h3>
			<span class="price">$4/-</span>
			<span class="quantity">qty : 09</span>
		</div>
	</div>
	<div class="Total"> Total : $23.3/- </div>
	<a href="#" class="btn">Checkout</a>
</div>

<form action="#" class="login-form">
	<h3>Admin Login</h3>
	<input type="email" placeholder="Admin email" class="box">
    <input type="password" placeholder="Admin password" class="box">
    <input type="submit" value="Login as Admin" class="btn">
</form>

</header>
<!-- header section  -->

<!-- admin dashboard -->
<section class="admin-dashboard" style="padding-top: 12rem;">
	<h1 class="heading"> admin <span>dashboard</span></h1>

	<!-- dashboard stats -->
	<div class="dashboard-stats">
		<div class="stat-card">
			<i class="fas fa-box"></i>
			<div class="stat-info">
				<h3 id="total-products">0</h3>
				<p>Total Products</p>
			</div>
		</div>
		
		<div class="stat-card">
			<i class="fas fa-shopping-bag"></i>
			<div class="stat-info">
				<h3 id="total-orders">0</h3>
				<p>Total Orders</p>
			</div>
		</div>
		
		<div class="stat-card">
			<i class="fas fa-users"></i>
			<div class="stat-info">
				<h3 id="total-customers">0</h3>
				<p>Total Customers</p>
			</div>
		</div>
		
		<div class="stat-card">
			<i class="fas fa-dollar-sign"></i>
			<div class="stat-info">
				<h3 id="total-revenue">$0</h3>
				<p>Total Revenue</p>
			</div>
		</div>
	</div>

	<!-- admin navigation tabs -->
	<div class="admin-tabs">
		<button class="tab-btn active" data-tab="products">Products</button>
		<button class="tab-btn" data-tab="orders">Orders</button>
		<button class="tab-btn" data-tab="customers">Customers</button>
		<button class="tab-btn" data-tab="blogs">Blogs</button>
		<button class="tab-btn" data-tab="reviews">Reviews</button>
		<button class="tab-btn" data-tab="website">Website Settings</button>
		<button class="tab-btn" data-tab="analytics">Analytics</button>
	</div>

	<!-- products management -->
	<div class="tab-content active" id="products-tab">
		<div class="section-header">
			<h2>Product Management</h2>
			<button class="btn" id="add-product-btn">
				<i class="fas fa-plus"></i> Add New Product
			</button>
		</div>

		<div class="products-table">
			<table>
				<thead>
					<tr>
						<th>ID</th>
						<th>Image</th>
						<th>Name</th>
						<th>Category</th>
						<th>Price</th>
						<th>Stock</th>
						<th>Status</th>
						<th>Actions</th>
					</tr>
				</thead>
				<tbody id="products-tbody">
					<!-- Products will be loaded here -->
				</tbody>
			</table>
		</div>
	</div>

	<!-- orders management -->
	<div class="tab-content" id="orders-tab">
		<div class="section-header">
			<h2>Order Management</h2>
			<select class="filter-select">
				<option value="all">All Orders</option>
				<option value="pending">Pending</option>
				<option value="processing">Processing</option>
				<option value="shipped">Shipped</option>
				<option value="delivered">Delivered</option>
			</select>
		</div>

		<div class="orders-table">
			<table>
				<thead>
					<tr>
						<th>Order ID</th>
						<th>Customer</th>
						<th>Date</th>
						<th>Total</th>
						<th>Status</th>
						<th>Actions</th>
					</tr>
				</thead>
				<tbody id="orders-tbody">
					<!-- Orders will be loaded here -->
				</tbody>
			</table>
		</div>
	</div>

	<!-- customers management -->
	<div class="tab-content" id="customers-tab">
		<div class="section-header">
			<h2>Customer Management</h2>
			<input type="search" placeholder="Search customers..." class="search-input">
		</div>

		<div class="customers-table">
			<table>
				<thead>
					<tr>
						<th>ID</th>
						<th>Name</th>
						<th>Email</th>
						<th>Phone</th>
						<th>Orders</th>
						<th>Total Spent</th>
						<th>Status</th>
						<th>Actions</th>
					</tr>
				</thead>
				<tbody id="customers-tbody">
					<!-- Customers will be loaded here -->
				</tbody>
			</table>
		</div>
	</div>

	<!-- blogs management -->
	<div class="tab-content" id="blogs-tab">
		<div class="section-header">
			<h2>Blogs Management</h2>
			<button class="btn" id="add-blog-btn">
				<i class="fas fa-plus"></i> Add New Blog
			</button>
		</div>

		<div class="content-grid" id="blogs-grid">
			<!-- Blogs will be loaded here -->
		</div>
	</div>

	<!-- reviews management -->
	<div class="tab-content" id="reviews-tab">
		<div class="section-header">
			<h2>Reviews Management</h2>
			<div class="filter-options">
				<select id="review-filter">
					<option value="all">All Reviews</option>
					<option value="pending">Pending Approval</option>
					<option value="approved">Approved</option>
					<option value="rejected">Rejected</option>
				</select>
			</div>
		</div>

		<div class="reviews-list" id="reviews-list">
			<!-- Reviews will be loaded here -->
		</div>
	</div>

	<!-- website settings -->
	<div class="tab-content" id="website-tab">
		<div class="section-header">
			<h2>Website Settings</h2>
		</div>

		<div class="settings-grid">
			<div class="setting-card">
				<h3><i class="fas fa-image"></i> Logo & Branding</h3>
				<div class="setting-content">
					<div class="current-logo">
						<img src="image/banner-img.jpg" alt="Current Logo" id="current-logo">
						<p>Current Logo</p>
					</div>
					<div class="logo-upload">
						<input type="file" id="logo-upload" accept="image/*">
						<label for="logo-upload" class="btn">
							<i class="fas fa-upload"></i> Upload New Logo
						</label>
					</div>
					<div class="form-group">
						<label>Website Title</label>
						<input type="text" id="website-title" value="Glossary Store">
					</div>
					<div class="form-group">
						<label>Website Tagline</label>
						<input type="text" id="website-tagline" value="Fresh & Organic Products">
					</div>
				</div>
			</div>

			<div class="setting-card">
				<h3><i class="fas fa-palette"></i> Theme Settings</h3>
				<div class="setting-content">
					<div class="color-picker">
						<label>Primary Color</label>
						<input type="color" id="primary-color" value="#008000">
					</div>
					<div class="color-picker">
						<label>Secondary Color</label>
						<input type="color" id="secondary-color" value="#333333">
					</div>
					<div class="form-group">
						<label>Font Family</label>
						<select id="font-family">
							<option value="Nunito">Nunito (Current)</option>
							<option value="Arial">Arial</option>
							<option value="Roboto">Roboto</option>
							<option value="Open Sans">Open Sans</option>
						</select>
					</div>
				</div>
			</div>

			<div class="setting-card">
				<h3><i class="fas fa-info-circle"></i> Contact Information</h3>
				<div class="setting-content">
					<div class="form-group">
						<label>Phone Number</label>
						<input type="tel" id="contact-phone" value="+****************">
					</div>
					<div class="form-group">
						<label>Email Address</label>
						<input type="email" id="contact-email" value="<EMAIL>">
					</div>
					<div class="form-group">
						<label>Address</label>
						<textarea id="contact-address" rows="3">123 Fresh Street, Organic City, Green State 12345</textarea>
					</div>
				</div>
			</div>

			<div class="setting-card">
				<h3><i class="fas fa-share-alt"></i> Social Media</h3>
				<div class="setting-content">
					<div class="form-group">
						<label><i class="fab fa-facebook"></i> Facebook</label>
						<input type="url" id="social-facebook" placeholder="https://facebook.com/glossarystore">
					</div>
					<div class="form-group">
						<label><i class="fab fa-twitter"></i> Twitter</label>
						<input type="url" id="social-twitter" placeholder="https://twitter.com/glossarystore">
					</div>
					<div class="form-group">
						<label><i class="fab fa-instagram"></i> Instagram</label>
						<input type="url" id="social-instagram" placeholder="https://instagram.com/glossarystore">
					</div>
					<div class="form-group">
						<label><i class="fab fa-youtube"></i> YouTube</label>
						<input type="url" id="social-youtube" placeholder="https://youtube.com/glossarystore">
					</div>
				</div>
			</div>
		</div>

		<div class="settings-actions">
			<button class="btn" onclick="saveWebsiteSettings()">
				<i class="fas fa-save"></i> Save All Settings
			</button>
			<button class="btn secondary" onclick="resetSettings()">
				<i class="fas fa-undo"></i> Reset to Default
			</button>
		</div>
	</div>

	<!-- analytics -->
	<div class="tab-content" id="analytics-tab">
		<div class="section-header">
			<h2>Analytics & Reports</h2>
			<select class="filter-select">
				<option value="week">This Week</option>
				<option value="month">This Month</option>
				<option value="year">This Year</option>
			</select>
		</div>

		<div class="analytics-grid">
			<div class="chart-container">
				<h3>Sales Overview</h3>
				<div class="chart-placeholder">
					<p>Sales chart will be displayed here</p>
				</div>
			</div>

			<div class="chart-container">
				<h3>Top Products</h3>
				<div class="chart-placeholder">
					<p>Top products chart will be displayed here</p>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Add Product Modal -->
<div class="modal" id="add-product-modal">
	<div class="modal-content">
		<span class="close">&times;</span>
		<h2>Add New Product</h2>

		<form id="add-product-form">
			<div class="form-group">
				<label>Product Name:</label>
				<input type="text" name="name" required>
			</div>

			<div class="form-group">
				<label>Category:</label>
				<select name="category" required>
					<option value="">Select Category</option>
					<option value="fruits">Fruits</option>
					<option value="vegetables">Vegetables</option>
					<option value="dairy">Dairy</option>
					<option value="meat">Meat</option>
					<option value="grains">Grains</option>
					<option value="beverages">Beverages</option>
				</select>
			</div>

			<div class="form-group">
				<label>Price:</label>
				<input type="number" name="price" step="0.01" required>
			</div>

			<div class="form-group">
				<label>Stock Quantity:</label>
				<input type="number" name="stock" required>
			</div>

			<div class="form-group">
				<label>Description:</label>
				<textarea name="description" rows="3"></textarea>
			</div>

			<div class="form-group">
				<label>Product Image:</label>
				<input type="file" name="image" accept="image/*">
			</div>

			<div class="form-actions">
				<button type="button" class="btn-cancel">Cancel</button>
				<button type="submit" class="btn">Add Product</button>
			</div>
		</form>
	</div>
</div>

<!-- Add Blog Modal -->
<div class="modal" id="blog-modal">
	<div class="modal-content">
		<span class="close">&times;</span>
		<h2 id="blog-modal-title">Add New Blog</h2>

		<form id="blog-form">
			<div class="form-group">
				<label>Blog Title</label>
				<input type="text" id="blog-title" required>
			</div>

			<div class="form-group">
				<label>Author</label>
				<input type="text" id="blog-author" required>
			</div>

			<div class="form-group">
				<label>Category</label>
				<select id="blog-category" required>
					<option value="">Select Category</option>
					<option value="Health">Health</option>
					<option value="Nutrition">Nutrition</option>
					<option value="Recipes">Recipes</option>
					<option value="Tips">Tips</option>
					<option value="Organic">Organic Living</option>
				</select>
			</div>

			<div class="form-group">
				<label>Featured Image</label>
				<input type="file" id="blog-image" accept="image/*">
			</div>

			<div class="form-group">
				<label>Excerpt</label>
				<textarea id="blog-excerpt" rows="3" placeholder="Brief description of the blog post"></textarea>
			</div>

			<div class="form-group">
				<label>Content</label>
				<textarea id="blog-content" rows="10" placeholder="Full blog content"></textarea>
			</div>

			<div class="form-group">
				<label>Tags (comma separated)</label>
				<input type="text" id="blog-tags" placeholder="health, nutrition, organic">
			</div>

			<div class="form-options">
				<label class="checkbox-container">
					<input type="checkbox" id="blog-published" checked>
					<span class="checkmark"></span>
					Publish immediately
				</label>
			</div>

			<div class="form-actions">
				<button type="button" class="btn-cancel">Cancel</button>
				<button type="submit" class="btn">Save Blog</button>
			</div>
		</form>
	</div>
</div>

<script src="js/script.js"></script>
<script src="js/auth.js"></script>
<script src="js/admin.js"></script>
<script src="js/management.js"></script>

</body>
</html>
